/*
 * Copyright (c) 2023, sakumisu
 *
 * SPDX-License-Identifier: Apache-2.0
 */
#ifndef USB_ERRNO_H
#define USB_ERRNO_H

#define USB_ERR_NOMEM    1
#define USB_ERR_INVAL    2
#define USB_ERR_NODEV    3
#define USB_ERR_NOTCONN  4
#define USB_ERR_NOTSUPP  5
#define USB_ERR_BUSY     6
#define USB_ERR_RANGE    7
#define USB_ERR_STALL    8
#define USB_ERR_BABBLE   9
#define USB_ERR_NAK      10
#define USB_ERR_DT       11
#define USB_ERR_IO       12
#define USB_ERR_SHUTDOWN 13
#define USB_ERR_TIMEOUT  14

#endif /* USB_ERRNO_H */
