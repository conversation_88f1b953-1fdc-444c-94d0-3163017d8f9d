# Copyright (c) 2023 HPMicro
# SPDX-License-Identifier: BSD-3-Clause

# openocd flash driver argument:
#   - option0:
#       [31:28] Flash probe type
#         0 - <PERSON>DP SDR / 1 - SFDP DDR
#         2 - 1-4-4 Read (0xEB, 24-bit address) / 3 - 1-2-2 Read(0xBB, 24-bit address)
#         4 - HyperFLASH 1.8V / 5 - HyperFLASH 3V
#         6 - OctaBus DDR (SPI -> OPI DDR)
#         8 - Xccela DDR (SPI -> OPI DDR)
#         10 - EcoXiP DDR (SPI -> OPI DDR)
#       [27:24] Command Pads after Power-on Reset
#         0 - SPI / 1 - DPI / 2 - QPI / 3 - OPI
#       [23:20] Command Pads after Configuring FLASH
#         0 - SPI / 1 - DPI / 2 - QPI / 3 - OPI
#       [19:16] Quad Enable Sequence (for the device support SFDP 1.0 only)
#         0 - Not needed
#         1 - QE bit is at bit 6 in Status Register 1
#         2 - QE bit is at bit1 in Status Register 2
#         3 - QE bit is at bit7 in Status Register 2
#         4 - QE bit is at bit1 in Status Register 2 and should be programmed by 0x31
#       [15:8] Dummy cycles
#         0 - Auto-probed / detected / default value
#         Others - User specified value, for DDR read, the dummy cycles should be 2 * cycles on FLASH datasheet
#       [7:4] Misc.
#         0 - Not used
#         1 - SPI mode
#         2 - Internal loopback
#         3 - External DQS
#       [3:0] Frequency option
#         1 - 30MHz / 2 - 50MHz / 3 - 66MHz / 4 - 80MHz / 5 - 100MHz / 6 - 120MHz / 7 - 133MHz / 8 - 166MHz
#   - option1:
#       [31:20]  Reserved
#       [19:16] IO voltage
#         0 - 3V / 1 - 1.8V
#       [15:12] Pin group
#         0 - 1st group / 1 - 2nd group
#       [11:8] Connection selection
#         0 - CA_CS0 / 1 - CB_CS0 / 2 - CA_CS0 + CB_CS0 (Two FLASH connected to CA and CB respectively)
#       [7:0] Drive Strength
#         0 - Default value

# xpi0 configs
#   - flash driver:     hpm_xpi
#   - flash ctrl index: 0xF3000000
#   - base address:     0x80000000
#   - flash size:       0x2000000
#   - flash option0:    0x7
flash bank xpi0 hpm_xpi 0x80000000 0x2000000 1 1 $_TARGET0 0xF3000000 0x6 0x1000

proc init_clock {} {
    $::_TARGET0 riscv dmi_write 0x39 0xF4002000
    $::_TARGET0 riscv dmi_write 0x3C 0x1

    $::_TARGET0 riscv dmi_write 0x39 0xF4002000
    $::_TARGET0 riscv dmi_write 0x3C 0x2

    $::_TARGET0 riscv dmi_write 0x39 0xF4000800
    $::_TARGET0 riscv dmi_write 0x3C 0xFFFFFFFF

    $::_TARGET0 riscv dmi_write 0x39 0xF4000810
    $::_TARGET0 riscv dmi_write 0x3C 0xFFFFFFFF

    $::_TARGET0 riscv dmi_write 0x39 0xF4000820
    $::_TARGET0 riscv dmi_write 0x3C 0xFFFFFFFF

    $::_TARGET0 riscv dmi_write 0x39 0xF4000830
    $::_TARGET0 riscv dmi_write 0x3C 0xFFFFFFFF
    echo "clocks has been enabled!"
}

$_TARGET0 configure -event reset-init {
    init_clock
}

$_TARGET0 configure -event gdb-attach {
    reset halt
}
