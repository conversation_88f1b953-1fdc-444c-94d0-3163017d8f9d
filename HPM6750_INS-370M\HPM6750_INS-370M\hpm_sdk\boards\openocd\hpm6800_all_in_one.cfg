# Copyright (c) 2024 HPMicro
# SPDX-License-Identifier: BSD-3-Clause
#
# assumptions:
#   - HPM_SDK_BASE has been defined as environment variable pointing to correct hpm_sdk path
#   - current directory is ${HPM_SDK_BASE}/boards/openocd
#
# usage:
# # connect hpm6800evk via ft2232, debugging single core
# *unix:
#   $ openocd -c "set HPM_SDK_BASE ${HPM_SDK_BASE}; set BOARD hpm6800evk; set PROBE ft2232;" -f hpm6800_all_in_one.cfg
# Windows DOS batch:
#   $ openocd -c "set HPM_SDK_BASE %HPM_SDK_BASE:\=/%; set BOARD hpm6800evk; set PROBE ft2232;" -f hpm6800_all_in_one.cfg
#
# # supported board to be set to BOARD:
#   - hpm6800evk
# # supported probes to be set to PROBE:
#   - ft2232
#   - ft232
#   - jlink
#   - cmsis_dap
#   - nds_aice_micro

set HPM_OPENOCD_CONFIG ${HPM_SDK_BASE}/boards/openocd

if { ![info exists PROBE ] } {
    set PROBE ft2232
}

if { ![info exists BOARD] } {
    set BOARD hpm6800evk
}

source ${HPM_OPENOCD_CONFIG}/probes/${PROBE}.cfg
source ${HPM_OPENOCD_CONFIG}/soc/hpm6880.cfg
source ${HPM_OPENOCD_CONFIG}/boards/${BOARD}.cfg
