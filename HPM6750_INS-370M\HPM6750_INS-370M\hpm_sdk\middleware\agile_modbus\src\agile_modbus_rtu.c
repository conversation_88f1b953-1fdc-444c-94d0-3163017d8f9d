/**
 * @file    agile_modbus_rtu.c
 * @brief   Agile Modbus package RTU source file
 * <AUTHOR> (<EMAIL>)
 * @date    2021-12-02
 *
 * @attention
 *
 * <h2><center>&copy; Copyright (c) 2021 <PERSON>.
 * All rights reserved.</center></h2>
 *
 */

#include "agile_modbus.h"

#if AGILE_MODBUS_USING_RTU

#include "agile_modbus_rtu.h"

/** @defgroup RTU RTU
 * @{
 */

/** @defgroup RTU_Private_Constants RTU Private Constants
 * @{
 */
/** Table of CRC values for high-order byte */
static const uint8_t _table_crc_hi[] =
    {
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40};

/** Table of CRC values for low-order byte */
static const uint8_t _table_crc_lo[] =
    {
        0x00, 0xC0, 0xC1, 0x01, 0xC3, 0x03, 0x02, 0xC2, 0xC6, 0x06,
        0x07, 0xC7, 0x05, 0xC5, 0xC4, 0x04, 0xCC, 0x0C, 0x0D, 0xCD,
        0x0F, 0xCF, 0xCE, 0x0E, 0x0A, 0xCA, 0xCB, 0x0B, 0xC9, 0x09,
        0x08, 0xC8, 0xD8, 0x18, 0x19, 0xD9, 0x1B, 0xDB, 0xDA, 0x1A,
        0x1E, 0xDE, 0xDF, 0x1F, 0xDD, 0x1D, 0x1C, 0xDC, 0x14, 0xD4,
        0xD5, 0x15, 0xD7, 0x17, 0x16, 0xD6, 0xD2, 0x12, 0x13, 0xD3,
        0x11, 0xD1, 0xD0, 0x10, 0xF0, 0x30, 0x31, 0xF1, 0x33, 0xF3,
        0xF2, 0x32, 0x36, 0xF6, 0xF7, 0x37, 0xF5, 0x35, 0x34, 0xF4,
        0x3C, 0xFC, 0xFD, 0x3D, 0xFF, 0x3F, 0x3E, 0xFE, 0xFA, 0x3A,
        0x3B, 0xFB, 0x39, 0xF9, 0xF8, 0x38, 0x28, 0xE8, 0xE9, 0x29,
        0xEB, 0x2B, 0x2A, 0xEA, 0xEE, 0x2E, 0x2F, 0xEF, 0x2D, 0xED,
        0xEC, 0x2C, 0xE4, 0x24, 0x25, 0xE5, 0x27, 0xE7, 0xE6, 0x26,
        0x22, 0xE2, 0xE3, 0x23, 0xE1, 0x21, 0x20, 0xE0, 0xA0, 0x60,
        0x61, 0xA1, 0x63, 0xA3, 0xA2, 0x62, 0x66, 0xA6, 0xA7, 0x67,
        0xA5, 0x65, 0x64, 0xA4, 0x6C, 0xAC, 0xAD, 0x6D, 0xAF, 0x6F,
        0x6E, 0xAE, 0xAA, 0x6A, 0x6B, 0xAB, 0x69, 0xA9, 0xA8, 0x68,
        0x78, 0xB8, 0xB9, 0x79, 0xBB, 0x7B, 0x7A, 0xBA, 0xBE, 0x7E,
        0x7F, 0xBF, 0x7D, 0xBD, 0xBC, 0x7C, 0xB4, 0x74, 0x75, 0xB5,
        0x77, 0xB7, 0xB6, 0x76, 0x72, 0xB2, 0xB3, 0x73, 0xB1, 0x71,
        0x70, 0xB0, 0x50, 0x90, 0x91, 0x51, 0x93, 0x53, 0x52, 0x92,
        0x96, 0x56, 0x57, 0x97, 0x55, 0x95, 0x94, 0x54, 0x9C, 0x5C,
        0x5D, 0x9D, 0x5F, 0x9F, 0x9E, 0x5E, 0x5A, 0x9A, 0x9B, 0x5B,
        0x99, 0x59, 0x58, 0x98, 0x88, 0x48, 0x49, 0x89, 0x4B, 0x8B,
        0x8A, 0x4A, 0x4E, 0x8E, 0x8F, 0x4F, 0x8D, 0x4D, 0x4C, 0x8C,
        0x44, 0x84, 0x85, 0x45, 0x87, 0x47, 0x46, 0x86, 0x82, 0x42,
        0x43, 0x83, 0x41, 0x81, 0x80, 0x40};
/**
 * @}
 */

/** @defgroup RTU_Private_Functions RTU Private Functions
 * @{
 */

/**
 * @brief   RTU CRC16 calculation
 * @param   buffer data pointer
 * @param   buffer_length data length
 * @return  CRC16 value
 */
static uint16_t agile_modbus_rtu_crc16(uint8_t *buffer, uint16_t buffer_length)
{
    uint8_t crc_hi = 0xFF; /* high CRC byte initialized */
    uint8_t crc_lo = 0xFF; /* low CRC byte initialized */
    unsigned int i;        /* will index into CRC lookup */

    /* pass through message buffer */
    while (buffer_length--) {
        i = crc_hi ^ *buffer++; /* calculate the CRC  */
        crc_hi = crc_lo ^ _table_crc_hi[i];
        crc_lo = _table_crc_lo[i];
    }

    return (crc_hi << 8 | crc_lo);
}

/**
 * @brief   RTU sets the address interface
 * @param   ctx modbus handle
 * @param   slave slave address
 * @return  0: success
 */
static int agile_modbus_rtu_set_slave(agile_modbus_t *ctx, int slave)
{
    ctx->slave = slave;
    return 0;
}

/**
 * @brief   RTU builds the basic request message interface (header message)
 * @param   ctx modbus handle
 * @param   function function code
 * @param   addr register address
 * @param   nb number of registers
 * @param   req data storage pointer
 * @return  data length
 */
static int agile_modbus_rtu_build_request_basis(agile_modbus_t *ctx, int function,
                                                int addr, int nb,
                                                uint8_t *req)
{
    req[0] = ctx->slave;
    req[1] = function;
    req[2] = addr >> 8;
    req[3] = addr & 0x00ff;
    req[4] = nb >> 8;
    req[5] = nb & 0x00ff;

    return AGILE_MODBUS_RTU_PRESET_REQ_LENGTH;
}

/**
 * @brief   RTU builds the basic response message interface (header message)
 * @param   sft modbus header parameter structure pointer
 * @param   rsp data storage pointer
 * @return  data length
 */
static int agile_modbus_rtu_build_response_basis(agile_modbus_sft_t *sft, uint8_t *rsp)
{
    rsp[0] = sft->slave;
    rsp[1] = sft->function;

    return AGILE_MODBUS_RTU_PRESET_RSP_LENGTH;
}

/**
 * @brief   RTU ready response interface
 * @note    This API will automatically subtract the AGILE_MODBUS_RTU_CHECKSUM_LENGTH length from req_length
 * @param   req request data pointer
 * @param   req_length request data length
 * @return  0 (RTU has no transaction identifier)
 */
static int agile_modbus_rtu_prepare_response_tid(const uint8_t *req, int *req_length)
{
    (void)req;
    (*req_length) -= AGILE_MODBUS_RTU_CHECKSUM_LENGTH;
    /* No TID */
    return 0;
}

/**
 * @brief   RTU pre-send data interface
 * @note    This API will calculate CRC16 and automatically fill in the tail
 * @param   req data storage pointer
 * @param   req_length existing data length
 * @return  data length
 */
static int agile_modbus_rtu_send_msg_pre(uint8_t *req, int req_length)
{
    uint16_t crc = agile_modbus_rtu_crc16(req, req_length);
    req[req_length++] = crc >> 8;
    req[req_length++] = crc & 0x00FF;

    return req_length;
}

/**
 * @brief   RTU check received data integrity interface (CRC16 comparison)
 * @param   ctx modbus handle
 * @param   msg Receive data pointer
 * @param   msg_length valid data length
 * @return  >0: valid data length; others: exception
 */
static int agile_modbus_rtu_check_integrity(agile_modbus_t *ctx, uint8_t *msg, const int msg_length)
{
    uint16_t crc_calculated;
    uint16_t crc_received;
    (void)ctx;
    crc_calculated = agile_modbus_rtu_crc16(msg, msg_length - 2);
    crc_received = (msg[msg_length - 2] << 8) | msg[msg_length - 1];

    /* Check CRC of msg */
    if (crc_calculated == crc_received)
        return msg_length;

    return -1;
}

/**
 * @brief   RTU pre-check confirmation interface (request response address comparison)
 * @note    If the request address is broadcast address 0, return success
 * @param   ctx modbus handle
 * @param   req request data pointer
 * @param   rsp response data pointer
 * @param   rsp_length response data length
 * @return  0: success; others: exception
 */
static int agile_modbus_rtu_pre_check_confirmation(agile_modbus_t *ctx, const uint8_t *req,
                                                   const uint8_t *rsp, int rsp_length)
{
    /* Check responding slave is the slave we requested (except for broacast
     * request) */
    (void)ctx;
    (void)rsp_length;
    if (req[0] != rsp[0] && req[0] != AGILE_MODBUS_BROADCAST_ADDRESS)
        return -1;

    return 0;
}

/**
 * @}
 */

/** @addtogroup RTU_Private_Constants
 * @{
 */

/**
 * @brief   RTU backend interface
 */
static const agile_modbus_backend_t agile_modbus_rtu_backend =
    {
        AGILE_MODBUS_BACKEND_TYPE_RTU,
        AGILE_MODBUS_RTU_HEADER_LENGTH,
        AGILE_MODBUS_RTU_CHECKSUM_LENGTH,
        AGILE_MODBUS_RTU_MAX_ADU_LENGTH,
        agile_modbus_rtu_set_slave,
        agile_modbus_rtu_build_request_basis,
        agile_modbus_rtu_build_response_basis,
        agile_modbus_rtu_prepare_response_tid,
        agile_modbus_rtu_send_msg_pre,
        agile_modbus_rtu_check_integrity,
        agile_modbus_rtu_pre_check_confirmation};

/**
 * @}
 */

/** @defgroup RTU_Exported_Functions RTU Exported Functions
 * @{
 */

/**
 * @brief   RTU initialization
 * @param   ctx RTU handle
 * @param   send_buf send buffer
 * @param   send_bufsz send buffer size
 * @param   read_buf receive buffer
 * @param   read_bufsz receive buffer size
 * @return  0: success
 */
int agile_modbus_rtu_init(agile_modbus_rtu_t *ctx, uint8_t *send_buf, int send_bufsz, uint8_t *read_buf, int read_bufsz)
{
    agile_modbus_common_init(&(ctx->_ctx), send_buf, send_bufsz, read_buf, read_bufsz);
    ctx->_ctx.backend = &agile_modbus_rtu_backend;
    ctx->_ctx.backend_data = ctx;

    return 0;
}

/**
 * @}
 */

/**
 * @}
 */

#endif /* AGILE_MODBUS_USING_RTU */
