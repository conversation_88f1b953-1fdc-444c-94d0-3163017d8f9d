<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=11"/>
<meta name="generator" content="Doxygen 1.9.2"/>
<meta name="viewport" content="width=device-width, initial-scale=1"/>
<title>Agile Modbus: Agile Modbus</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="navtree.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="resize.js"></script>
<script type="text/javascript" src="navtreedata.js"></script>
<script type="text/javascript" src="navtree.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">Agile Modbus<span id="projectnumber">&#160;1.1.3</span>
   </div>
   <div id="projectbrief">Lightweight modbus protocol stack.</div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.9.2 -->
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
var searchBox = new SearchBox("searchBox", "search",'Search','.html');
/* @license-end */
</script>
<script type="text/javascript" src="menudata.js"></script>
<script type="text/javascript" src="menu.js"></script>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(function() {
  initMenu('',true,false,'search.php','Search');
  $(document).ready(function() { init_search(); });
});
/* @license-end */
</script>
<div id="main-nav"></div>
</div><!-- top -->
<div id="side-nav" class="ui-resizable side-nav-resizable">
  <div id="nav-tree">
    <div id="nav-tree-contents">
      <div id="nav-sync" class="sync"></div>
    </div>
  </div>
  <div id="splitbar" style="-moz-user-select:none;" 
       class="ui-resizable-handle">
  </div>
</div>
<script type="text/javascript">
/* @license magnet:?xt=urn:btih:d3d9a9a6595521f9666a5e94cc830dab83b65699&amp;dn=expat.txt MIT */
$(document).ready(function(){initNavTree('index.html',''); initResizable(); });
/* @license-end */
</script>
<div id="doc-content">
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div><div class="header">
  <div class="headertitle"><div class="title">Agile Modbus </div></div>
</div><!--header-->
<div class="contents">
<div class="textblock"><p ><a class="anchor" id="md_C__Users_25440_Desktop_agile_modbus_README"></a> </p>
<h1><a class="anchor" id="autotoc_md20"></a>
1. Introduction</h1>
<p >Agile Modbus is a lightweight modbus protocol stack that meets the needs of users in any scenario.</p>
<p ><img src="./figures/ModbusProtocol.jpg" alt="ModbusProtocol" class="inline"/></p>
<ul>
<li>Online documentation: <a href="https://loogg.github.io/agile_modbus/">API Manual</a></li>
<li>The <code>examples</code> folder provides examples on PC</li>
<li>See examples on MCU <a href="https://github.com/loogg/agile_modbus_mcu_demos">mcu_demos</a></li>
<li>Bootloader based on RT-Thread on AT32F437 that supports Modbus firmware upgrade: <a href="https://github.com/loogg/AT32F437_Boot">AT32F437_Boot</a></li>
<li>Bootloader based on RT-Thread on HPM6750 that supports Modbus firmware upgrade: <a href="https://github.com/loogg/HPM6750_Boot">HPM6750_Boot</a></li>
</ul>
<h2><a class="anchor" id="autotoc_md21"></a>
1.1. Features</h2>
<ol type="1">
<li>Supports rtu and tcp protocols, is developed using pure C, does not involve any hardware interface, and can be used directly on any form of hardware.</li>
<li>Since it is developed using pure C and does not involve hardware, it can run the tcp protocol on the serial port and the rtu protocol on the network.</li>
<li>Support custom protocols compliant with modbus format.</li>
<li>Supports multiple masters and multiple slaves at the same time.</li>
<li>It is easy to use. You only need to initialize the rtu or tcp handle and call the corresponding API to package and unpack.</li>
</ol>
<h2><a class="anchor" id="autotoc_md22"></a>
1.2. Directory structure</h2>
<table class="markdownTable">
<tr class="markdownTableHead">
<th class="markdownTableHeadNone">Name   </th><th class="markdownTableHeadNone">Description    </th></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">doc   </td><td class="markdownTableBodyNone">documentation    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">examples   </td><td class="markdownTableBodyNone">examples    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">figures   </td><td class="markdownTableBodyNone">materials    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">inc   </td><td class="markdownTableBodyNone">header file    </td></tr>
<tr class="markdownTableRowOdd">
<td class="markdownTableBodyNone">src   </td><td class="markdownTableBodyNone">source code    </td></tr>
<tr class="markdownTableRowEven">
<td class="markdownTableBodyNone">util   </td><td class="markdownTableBodyNone">Provides simple and practical components   </td></tr>
</table>
<h2><a class="anchor" id="autotoc_md23"></a>
1.3. License</h2>
<p >Agile Modbus complies with the <code>Apache-2.0</code> license, see the <code>LICENSE</code> file for details.</p>
<h1><a class="anchor" id="autotoc_md24"></a>
2. Use Agile Modbus</h1>
<p >Please view the help document <a href="./doc/doxygen/Agile_Modbus.chm">doc/doxygen/Agile_Modbus.chm</a></p>
<h2><a class="anchor" id="autotoc_md25"></a>
2.1. Transplantation</h2>
<ul>
<li><p class="startli">Users need to implement the <code>send data</code>, <code>wait for data reception to end</code> and <code>clear the receive buffer</code> functions of the hardware interface</p>
<p class="startli">Regarding <code>waiting for data reception to end</code>, the following ideas are provided:</p><ol type="1">
<li><p class="startli">General method</p>
<p class="startli">Every 20 /50 ms (this time can be set according to the baud rate and hardware, here is just a reference value) reads data from the hardware interface and stores it in the buffer and updates the offset until it cannot be read or the buffer is full. , exit reading.</p>
<p class="startli">This applies to both bare metal and operating systems, which can accomplish blocking via <code>select</code> or <code>semaphore</code>.</p>
</li>
<li><p class="startli">Serial port <code>DMA + IDLE</code> interrupt mode</p>
<p class="startli">Configure the <code>DMA + IDLE</code> interrupt, enable the flag in the interrupt, and determine whether the flag is set in the application program.</p>
<p class="startli">However, this solution is prone to problems. If the data bytes are slightly staggered, it will not be a frame. The first option is recommended.</p>
</li>
</ol>
</li>
<li>Host:<ol type="1">
<li><code>agile_modbus_rtu_init</code> / <code>agile_modbus_tcp_init</code> initializes <code>RTU/TCP</code> environment</li>
<li><code>agile_modbus_set_slave</code> sets the slave address</li>
<li><code>Clear the receive cache</code></li>
<li><code>agile_modbus_serialize_xxx</code> package request data</li>
<li><code>Send data</code></li>
<li><code>Waiting for data reception to end</code></li>
<li><code>agile_modbus_deserialize_xxx</code> Parse response data</li>
<li>Data processed by users</li>
</ol>
</li>
<li>Slave machine:<ol type="1">
<li>Implement the <code>agile_modbus_slave_callback_t</code> type callback function</li>
<li><code>agile_modbus_rtu_init</code> / <code>agile_modbus_tcp_init</code> initializes <code>RTU/TCP</code> environment</li>
<li><code>agile_modbus_set_slave</code> sets the slave address</li>
<li><code>Waiting for data reception to end</code></li>
<li><code>agile_modbus_slave_handle</code> processes request data</li>
<li><code>Clear the receive buffer</code> (optional)</li>
<li><code>Send data</code></li>
</ol>
</li>
<li><p class="startli">Special function code</p>
<p class="startli">You need to call the <code>agile_modbus_set_compute_meta_length_after_function_cb</code> and <code>agile_modbus_set_compute_data_length_after_meta_cb</code> APIs to set the callbacks for special function codes to be processed in master-slave mode.</p><ul>
<li><p class="startli"><code>agile_modbus_set_compute_meta_length_after_function_cb</code></p>
<p class="startli"><code>msg_type == AGILE_MODBUS_MSG_INDICATION</code>: Returns the data element length of the host request message (uint8_t type). If it is not a special function code, 0 must be returned.</p>
<p class="startli"><code>msg_type == MSG_CONFIRMATION</code>: Returns the data element length (uint8_t type) of the slave response message. If it is not a special function code, 1 must be returned.</p>
</li>
<li><p class="startli"><code>agile_modbus_set_compute_data_length_after_meta_cb</code></p>
<p class="startli"><code>msg_type == AGILE_MODBUS_MSG_INDICATION</code>: Returns the data length after the data element of the host request message. If it is not a special function code, 0 must be returned.</p>
<p class="startli"><code>msg_type == MSG_CONFIRMATION</code>: Returns the data length after the data element of the slave response message. If it is not a special function code, it must return 0.</p>
</li>
</ul>
</li>
<li><p class="startli"><code>agile_modbus_rtu_init</code> / <code>agile_modbus_tcp_init</code></p>
<p class="startli">When initializing the <code>RTU/TCP</code> environment, the user needs to pass in the <code>send buffer</code> and <code>receive buffer</code>. It is recommended that the size of both buffers is <code>AGILE_MODBUS_MAX_ADU_LENGTH</code> (260) bytes. <code>Special function code</code> is determined by the user according to the agreement.</p>
<p class="startli">But for small memory MCUs, these two buffers can also be set small, and all APIs will judge the buffer size:</p>
<p class="startli">Send buffer setting: If <code>expected request data length</code> or <code>expected response data length</code> is greater than <code>set send buffer size</code>, an exception is returned.</p>
<p class="startli">Receive buffer setting: If the <code>message length requested by the host</code> is greater than the <code>set receive buffer size</code>, an exception will be returned. This is reasonable. When a small memory MCU is used as a slave, certain function codes must be restricted.</p>
</li>
</ul>
<h2><a class="anchor" id="autotoc_md26"></a>
2.2. Host</h2>
<p >See <code>2.1. Transplantation</code>.</p>
<h2><a class="anchor" id="autotoc_md27"></a>
2.3. Slave machine</h2>
<h3><a class="anchor" id="autotoc_md28"></a>
2.3.1. Interface description</h3>
<ul>
<li><p class="startli">Introduction to <code>agile_modbus_slave_handle</code></p>
<div class="fragment"><div class="line"><span class="keywordtype">int</span> <a class="code hl_function" href="group___slave___operation___functions.html#ga9ddc38e7ac14384c02b07b8927165247">agile_modbus_slave_handle</a>(<a class="code hl_struct" href="structagile__modbus.html">agile_modbus_t</a> *ctx, <span class="keywordtype">int</span> msg_length, uint8_t slave_strict,</div>
<div class="line">                              <a class="code hl_typedef" href="group___slave___exported___types.html#gae66e6077fe07d589d91121ac8874541a">agile_modbus_slave_callback_t</a> slave_cb, <span class="keyword">const</span> <span class="keywordtype">void</span> *slave_data, <span class="keywordtype">int</span> *frame_length)</div>
<div class="ttc" id="agroup___slave___exported___types_html_gae66e6077fe07d589d91121ac8874541a"><div class="ttname"><a href="group___slave___exported___types.html#gae66e6077fe07d589d91121ac8874541a">agile_modbus_slave_callback_t</a></div><div class="ttdeci">int(* agile_modbus_slave_callback_t)(agile_modbus_t *ctx, struct agile_modbus_slave_info *slave_info, const void *data)</div><div class="ttdoc">Slave callback function.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus_8h_source.html#l00265">agile_modbus.h:265</a></div></div>
<div class="ttc" id="agroup___slave___operation___functions_html_ga9ddc38e7ac14384c02b07b8927165247"><div class="ttname"><a href="group___slave___operation___functions.html#ga9ddc38e7ac14384c02b07b8927165247">agile_modbus_slave_handle</a></div><div class="ttdeci">int agile_modbus_slave_handle(agile_modbus_t *ctx, int msg_length, uint8_t slave_strict, agile_modbus_slave_callback_t slave_cb, const void *slave_data, int *frame_length)</div><div class="ttdoc">slave data processing</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus_8c_source.html#l01203">agile_modbus.c:1203</a></div></div>
<div class="ttc" id="astructagile__modbus_html"><div class="ttname"><a href="structagile__modbus.html">agile_modbus</a></div><div class="ttdoc">Agile Modbus structure.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus_8h_source.html#l00217">agile_modbus.h:217</a></div></div>
</div><!-- fragment --><p class="startli">msg_length: The length of data received after <code>waiting for the end of data reception</code>.</p>
<p class="startli">slave_strict: slave address strictness check (0: Do not judge whether the address is consistent, it will be processed by user callback; 1: The address must be consistent, otherwise the callback will not be called and the response data will not be packaged).</p>
<p class="startli">slave_cb: <code>agile_modbus_slave_callback_t</code> type callback function, implemented and passed in by the user. If it is NULL, all function codes can respond and are successful, but the register data is still 0.</p>
<p class="startli">slave_data: slave callback function private data.</p>
<p class="startli">frame_length: Get the length of the parsed modbus data frame. The meaning of this parameter is:</p><ol type="1">
<li>There is dirty data at the end: it can still be parsed successfully and tells the user the real modbus frame length, which the user can process.</li>
<li>Data sticky packet: The data consists of a complete frame of modbus data + a partial modbus data frame. After the user obtains the real modbus frame length, he can remove the processed modbus data frame and read the hardware interface data and the current one again. Part of the modbus data frame forms a new frame</li>
<li>This parameter is often used when modbus broadcast transmits big data (such as custom function code broadcast to upgrade firmware). Ordinary slave responses are one question and one answer, and only the complete data frame is processed. It is recommended to Execute <code>clear receive cache</code></li>
</ol>
</li>
<li><p class="startli">Introduction to <code>agile_modbus_slave_callback_t</code></p>
<div class="fragment"><div class="line"> </div>
<div class="line"><span class="keyword">typedef</span> int (*<a class="code hl_typedef" href="group___slave___exported___types.html#gae66e6077fe07d589d91121ac8874541a">agile_modbus_slave_callback_t</a>)(<a class="code hl_struct" href="structagile__modbus.html">agile_modbus_t</a> *ctx, <span class="keyword">struct </span><a class="code hl_struct" href="structagile__modbus__slave__info.html">agile_modbus_slave_info</a> *slave_info, <span class="keyword">const</span> <span class="keywordtype">void</span> *data);</div>
<div class="ttc" id="astructagile__modbus__slave__info_html"><div class="ttname"><a href="structagile__modbus__slave__info.html">agile_modbus_slave_info</a></div><div class="ttdoc">Agile Modbus slave information structure.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus_8h_source.html#l00246">agile_modbus.h:246</a></div></div>
</div><!-- fragment --><p class="startli"><code><a class="el" href="structagile__modbus__slave__info.html" title="Agile Modbus slave information structure.">agile_modbus_slave_info</a></code>:</p>
<p class="startli">sft: Contains slave address and function code attributes, which can be used in callbacks</p>
<p class="startli">rsp_length: response data length pointer, its value needs to be updated when processing <code>special function code</code> in the callback, otherwise <b>not allowed to change</b></p>
<p class="startli">address: register address (not used by all function codes)</p>
<p class="startli">nb: number (not used by all function codes)</p>
<p class="startli">buf: data field required by different function codes (not used by all function codes)</p>
<p class="startli">send_index: the current index of the send buffer (not used by all function codes)</p>
</li>
<li><code><a class="el" href="structagile__modbus__slave__info.html" title="Agile Modbus slave information structure.">agile_modbus_slave_info</a></code> used by different function codes<ul>
<li><p class="startli">AGILE_MODBUS_FC_READ_COILS、AGILE_MODBUS_FC_READ_DISCRETE_INPUTS</p>
<p class="startli">The <code>address</code>, <code>nb</code>, and <code>send_index</code> attributes need to be used, and the <code>agile_modbus_slave_io_set</code> API needs to be called to store the IO data in the data area starting from <code>ctx-&gt;send_buf + send_index</code>.</p>
</li>
<li><p class="startli">AGILE_MODBUS_FC_READ_HOLDING_REGISTERS、AGILE_MODBUS_FC_READ_INPUT_REGISTERS</p>
<p class="startli">The <code>address</code>, <code>nb</code>, and <code>send_index</code> attributes need to be used, and the <code>agile_modbus_slave_register_set</code> API needs to be called to store the register data in the data area starting from <code>ctx-&gt;send_buf + send_index</code>.</p>
</li>
<li><p class="startli">AGILE_MODBUS_FC_WRITE_SINGLE_COIL、AGILE_MODBUS_FC_WRITE_SINGLE_REGISTER</p>
<p class="startli">You need to use the <code>address</code> and <code>buf</code> attributes, force <code>buf</code> to the <code>int *</code> type, and get the value and store it in a register.</p>
</li>
<li><p class="startli">AGILE_MODBUS_FC_WRITE_MULTIPLE_COILS</p>
<p class="startli">The <code>address</code>, <code>nb</code>, <code>buf</code> attributes need to be used, and the <code>agile_modbus_slave_io_get</code> API needs to be called to obtain the IO data to be written.</p>
</li>
<li><p class="startli">AGILE_MODBUS_FC_WRITE_MULTIPLE_REGISTERS</p>
<p class="startli">The <code>address</code>, <code>nb</code>, and <code>buf</code> attributes need to be used, and the <code>agile_modbus_slave_register_get</code> API needs to be called to obtain the register data to be written.</p>
</li>
<li><p class="startli">AGILE_MODBUS_FC_MASK_WRITE_REGISTER</p>
<p class="startli">You need to use the <code>address</code> and <code>buf</code> attributes, pass <code>(buf[0] &lt;&lt; 8) + buf[1]</code> to get the <code>and</code> value, pass <code>(buf[2] &lt;&lt; 8) + buf[3 ]</code> Gets the <code>or</code> value. Get the register value <code>data</code>, perform the <code>data = (data &amp; and) | (or &amp; (~and))</code> operation to update the <code>data</code> value, and write it to the register.</p>
</li>
<li><p class="startli">AGILE_MODBUS_FC_WRITE_AND_READ_REGISTERS</p>
<p class="startli">You need to use the <code>address</code>, <code>buf</code>, <code>send_index</code> attributes, pass <code>(buf[0] &lt;&lt; 8) + buf[1]</code> to get the number of registers to be read, pass <code>(buf[2] &lt;&lt; 8) + buf[3]</code> Get the register address to be written, and use <code>(buf[4] &lt;&lt; 8) + buf[5]</code> to get the number of registers to be written. You need to call the <code>agile_modbus_slave_register_get</code> API to obtain the register data to be written, and call the <code>agile_modbus_slave_register_set</code> API to store the register data in the data area starting from <code>ctx-&gt;send_buf + send_index</code>.</p>
</li>
<li><p class="startli">Custom function code</p>
<p class="startli">You need to use the <code>send_index</code>, <code>nb</code>, and <code>buf</code> attributes, and the user processes the data in the callback.</p>
<p class="startli">send_index: current index of send buffer</p>
<p class="startli">nb: PUD - 1, which is the modbus data field length</p>
<p class="startli">buf: starting position of modbus data field</p>
<p class="startli"><b>Note</b>: After the user fills data into the send buffer in the callback, the <code>rsp_length</code> value of <code><a class="el" href="structagile__modbus__slave__info.html" title="Agile Modbus slave information structure.">agile_modbus_slave_info</a></code> needs to be updated.</p>
</li>
</ul>
</li>
</ul>
<h3><a class="anchor" id="autotoc_md29"></a>
2.3.2. Simple slave access interface</h3>
<p >Agile Modbus provides an implementation of <code>agile_modbus_slave_callback_t</code>, allowing users to access it simply and conveniently.</p>
<p >See <a href="./examples/slave">examples/slave</a> for examples of usage.</p>
<p >How to use:</p>
<div class="fragment"><div class="line"><span class="preprocessor">#include &quot;<a class="code" href="agile__modbus_8h.html">agile_modbus.h</a>&quot;</span></div>
<div class="line"><span class="preprocessor">#include &quot;<a class="code" href="agile__modbus__slave__util_8h.html">agile_modbus_slave_util.h</a>&quot;</span></div>
<div class="line"> </div>
<div class="line"><span class="keyword">const</span> <a class="code hl_struct" href="structagile__modbus__slave__util.html">agile_modbus_slave_util_t</a> slave_util = {</div>
<div class="line">  <span class="comment">/* User implementation */</span></div>
<div class="line"> </div>
<div class="line">};</div>
<div class="line"> </div>
<div class="line"><a class="code hl_function" href="group___slave___operation___functions.html#ga9ddc38e7ac14384c02b07b8927165247">agile_modbus_slave_handle</a>(ctx, read_len, 0, <a class="code hl_function" href="group___s_l_a_v_e___u_t_i_l___exported___functions.html#ga6d7613fbda26c40a969df673cf9b8c5b">agile_modbus_slave_util_callback</a>, &amp;slave_util, NULL);</div>
<div class="ttc" id="aagile__modbus_8h_html"><div class="ttname"><a href="agile__modbus_8h.html">agile_modbus.h</a></div><div class="ttdoc">Agile Modbus software package common header file.</div></div>
<div class="ttc" id="aagile__modbus__slave__util_8h_html"><div class="ttname"><a href="agile__modbus__slave__util_8h.html">agile_modbus_slave_util.h</a></div><div class="ttdoc">The simple slave access header file provided by the Agile Modbus software package.</div></div>
<div class="ttc" id="agroup___s_l_a_v_e___u_t_i_l___exported___functions_html_ga6d7613fbda26c40a969df673cf9b8c5b"><div class="ttname"><a href="group___s_l_a_v_e___u_t_i_l___exported___functions.html#ga6d7613fbda26c40a969df673cf9b8c5b">agile_modbus_slave_util_callback</a></div><div class="ttdeci">int agile_modbus_slave_util_callback(agile_modbus_t *ctx, struct agile_modbus_slave_info *slave_info, const void *data)</div><div class="ttdoc">Slave callback function.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8c_source.html#l00387">agile_modbus_slave_util.c:387</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util_html"><div class="ttname"><a href="structagile__modbus__slave__util.html">agile_modbus_slave_util</a></div><div class="ttdoc">slave function structure</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00048">agile_modbus_slave_util.h:48</a></div></div>
</div><!-- fragment --><ul>
<li>Introduction to <code>agile_modbus_slave_util_callback</code><ul>
<li>An implementation of <code>agile_modbus_slave_callback_t</code> provided by Agile Modbus, which requires <code>agile_modbus_slave_util_t</code> type variable pointer as private data.</li>
<li>The private data is NULL, all function codes can respond and are successful, but the register data is still 0.</li>
</ul>
</li>
<li><p class="startli">Introduction to <code>agile_modbus_slave_util_t</code></p>
<div class="fragment"><div class="line"><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="structagile__modbus__slave__util.html">agile_modbus_slave_util</a> {</div>
<div class="line">    <span class="keyword">const</span> <a class="code hl_struct" href="structagile__modbus__slave__util__map.html">agile_modbus_slave_util_map_t</a> *<a class="code hl_variable" href="structagile__modbus__slave__util.html#adf60d2aacae135df7fb77f1fd567a0f3">tab_bits</a>;                                            </div>
<div class="line">    <span class="keywordtype">int</span> <a class="code hl_variable" href="structagile__modbus__slave__util.html#a54d8cd5ea2ece03fc4748f857d433470">nb_bits</a>;                                                                              </div>
<div class="line">    <span class="keyword">const</span> <a class="code hl_struct" href="structagile__modbus__slave__util__map.html">agile_modbus_slave_util_map_t</a> *<a class="code hl_variable" href="structagile__modbus__slave__util.html#ab8dd5a6fa6cb534aa90460cf9a71ae6f">tab_input_bits</a>;                                      </div>
<div class="line">    <span class="keywordtype">int</span> <a class="code hl_variable" href="structagile__modbus__slave__util.html#a81fb44a47ea6e8dc7eb190b87ddb6dc4">nb_input_bits</a>;                                                                        </div>
<div class="line">    <span class="keyword">const</span> <a class="code hl_struct" href="structagile__modbus__slave__util__map.html">agile_modbus_slave_util_map_t</a> *<a class="code hl_variable" href="structagile__modbus__slave__util.html#ae21eece6d89b5d46839496e9281108f8">tab_registers</a>;                                       </div>
<div class="line">    <span class="keywordtype">int</span> <a class="code hl_variable" href="structagile__modbus__slave__util.html#ac7c7bd2cc1687ae7c291a054d3071114">nb_registers</a>;                                                                         </div>
<div class="line">    <span class="keyword">const</span> <a class="code hl_struct" href="structagile__modbus__slave__util__map.html">agile_modbus_slave_util_map_t</a> *<a class="code hl_variable" href="structagile__modbus__slave__util.html#a67c8df610dcb3f8dee4d32ed920f4886">tab_input_registers</a>;                                 </div>
<div class="line">    <span class="keywordtype">int</span> <a class="code hl_variable" href="structagile__modbus__slave__util.html#a3c2c64bb74a0cab7f34f303fa3a37b31">nb_input_registers</a>;                                                                   </div>
<div class="line">    int (*<a class="code hl_variable" href="structagile__modbus__slave__util.html#a134270ba34165c677e85490b2bb6719c">addr_check</a>)(<a class="code hl_struct" href="structagile__modbus.html">agile_modbus_t</a> *ctx, <span class="keyword">struct </span><a class="code hl_struct" href="structagile__modbus__slave__info.html">agile_modbus_slave_info</a> *slave_info);       </div>
<div class="line">    int (*<a class="code hl_variable" href="structagile__modbus__slave__util.html#a70de0b2f19492b188c99ba332e2b2ffa">special_function</a>)(<a class="code hl_struct" href="structagile__modbus.html">agile_modbus_t</a> *ctx, <span class="keyword">struct </span><a class="code hl_struct" href="structagile__modbus__slave__info.html">agile_modbus_slave_info</a> *slave_info); </div>
<div class="line">    int (*<a class="code hl_variable" href="structagile__modbus__slave__util.html#abc6323db9d3873c30bee0e7efe96f4b8">done</a>)(<a class="code hl_struct" href="structagile__modbus.html">agile_modbus_t</a> *ctx, <span class="keyword">struct </span><a class="code hl_struct" href="structagile__modbus__slave__info.html">agile_modbus_slave_info</a> *slave_info, <span class="keywordtype">int</span> ret);    </div>
<div class="line">} <a class="code hl_typedef" href="group___s_l_a_v_e___u_t_i_l___exported___types.html#ga48cf733bea0cf7ad09b52b139348de50">agile_modbus_slave_util_t</a>;</div>
<div class="ttc" id="agroup___s_l_a_v_e___u_t_i_l___exported___types_html_ga48cf733bea0cf7ad09b52b139348de50"><div class="ttname"><a href="group___s_l_a_v_e___u_t_i_l___exported___types.html#ga48cf733bea0cf7ad09b52b139348de50">agile_modbus_slave_util_t</a></div><div class="ttdeci">struct agile_modbus_slave_util agile_modbus_slave_util_t</div><div class="ttdoc">slave function structure</div></div>
<div class="ttc" id="astructagile__modbus__slave__util__map_html"><div class="ttname"><a href="structagile__modbus__slave__util__map.html">agile_modbus_slave_util_map</a></div><div class="ttdoc">slave register mapping structure</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00038">agile_modbus_slave_util.h:38</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util_html_a134270ba34165c677e85490b2bb6719c"><div class="ttname"><a href="structagile__modbus__slave__util.html#a134270ba34165c677e85490b2bb6719c">agile_modbus_slave_util::addr_check</a></div><div class="ttdeci">int(* addr_check)(agile_modbus_t *ctx, struct agile_modbus_slave_info *slave_info)</div><div class="ttdoc">Address checking interface.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00057">agile_modbus_slave_util.h:57</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util_html_a3c2c64bb74a0cab7f34f303fa3a37b31"><div class="ttname"><a href="structagile__modbus__slave__util.html#a3c2c64bb74a0cab7f34f303fa3a37b31">agile_modbus_slave_util::nb_input_registers</a></div><div class="ttdeci">int nb_input_registers</div><div class="ttdoc">Input register definition array number.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00056">agile_modbus_slave_util.h:56</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util_html_a54d8cd5ea2ece03fc4748f857d433470"><div class="ttname"><a href="structagile__modbus__slave__util.html#a54d8cd5ea2ece03fc4748f857d433470">agile_modbus_slave_util::nb_bits</a></div><div class="ttdeci">int nb_bits</div><div class="ttdoc">The number of coil register definition arrays.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00050">agile_modbus_slave_util.h:50</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util_html_a67c8df610dcb3f8dee4d32ed920f4886"><div class="ttname"><a href="structagile__modbus__slave__util.html#a67c8df610dcb3f8dee4d32ed920f4886">agile_modbus_slave_util::tab_input_registers</a></div><div class="ttdeci">const agile_modbus_slave_util_map_t * tab_input_registers</div><div class="ttdoc">Input register definition array.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00055">agile_modbus_slave_util.h:55</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util_html_a70de0b2f19492b188c99ba332e2b2ffa"><div class="ttname"><a href="structagile__modbus__slave__util.html#a70de0b2f19492b188c99ba332e2b2ffa">agile_modbus_slave_util::special_function</a></div><div class="ttdeci">int(* special_function)(agile_modbus_t *ctx, struct agile_modbus_slave_info *slave_info)</div><div class="ttdoc">Special function code processing interface.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00058">agile_modbus_slave_util.h:58</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util_html_a81fb44a47ea6e8dc7eb190b87ddb6dc4"><div class="ttname"><a href="structagile__modbus__slave__util.html#a81fb44a47ea6e8dc7eb190b87ddb6dc4">agile_modbus_slave_util::nb_input_bits</a></div><div class="ttdeci">int nb_input_bits</div><div class="ttdoc">The number of discrete input register definition arrays.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00052">agile_modbus_slave_util.h:52</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util_html_ab8dd5a6fa6cb534aa90460cf9a71ae6f"><div class="ttname"><a href="structagile__modbus__slave__util.html#ab8dd5a6fa6cb534aa90460cf9a71ae6f">agile_modbus_slave_util::tab_input_bits</a></div><div class="ttdeci">const agile_modbus_slave_util_map_t * tab_input_bits</div><div class="ttdoc">Discrete input register definition array.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00051">agile_modbus_slave_util.h:51</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util_html_abc6323db9d3873c30bee0e7efe96f4b8"><div class="ttname"><a href="structagile__modbus__slave__util.html#abc6323db9d3873c30bee0e7efe96f4b8">agile_modbus_slave_util::done</a></div><div class="ttdeci">int(* done)(agile_modbus_t *ctx, struct agile_modbus_slave_info *slave_info, int ret)</div><div class="ttdoc">Processing end interface.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00059">agile_modbus_slave_util.h:59</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util_html_ac7c7bd2cc1687ae7c291a054d3071114"><div class="ttname"><a href="structagile__modbus__slave__util.html#ac7c7bd2cc1687ae7c291a054d3071114">agile_modbus_slave_util::nb_registers</a></div><div class="ttdeci">int nb_registers</div><div class="ttdoc">Number of holding register definition arrays.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00054">agile_modbus_slave_util.h:54</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util_html_adf60d2aacae135df7fb77f1fd567a0f3"><div class="ttname"><a href="structagile__modbus__slave__util.html#adf60d2aacae135df7fb77f1fd567a0f3">agile_modbus_slave_util::tab_bits</a></div><div class="ttdeci">const agile_modbus_slave_util_map_t * tab_bits</div><div class="ttdoc">Coil register definition array.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00049">agile_modbus_slave_util.h:49</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util_html_ae21eece6d89b5d46839496e9281108f8"><div class="ttname"><a href="structagile__modbus__slave__util.html#ae21eece6d89b5d46839496e9281108f8">agile_modbus_slave_util::tab_registers</a></div><div class="ttdeci">const agile_modbus_slave_util_map_t * tab_registers</div><div class="ttdoc">Holding register definition array.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00053">agile_modbus_slave_util.h:53</a></div></div>
</div><!-- fragment --><ul>
<li><p class="startli">Register related</p>
<p class="startli">Users need to implement the definitions of <code>bits</code>, <code>input_bits</code>, <code>registers</code> and <code>input_registers</code>. If a register is defined as NULL, the function code corresponding to the register can respond and is successful, but the register data is all 0.</p>
</li>
<li><p class="startli">Interface calling process</p>
<p class="startli"><img src="./figures/agile_modbus_slave_util_callback.png" alt="agile_modbus_slave_util_callback" class="inline"/></p>
</li>
</ul>
</li>
<li><p class="startli">Introduction to <code><a class="el" href="structagile__modbus__slave__util__map.html" title="slave register mapping structure">agile_modbus_slave_util_map</a></code></p>
<div class="fragment"><div class="line"><span class="keyword">typedef</span> <span class="keyword">struct </span><a class="code hl_struct" href="structagile__modbus__slave__util__map.html">agile_modbus_slave_util_map</a> {</div>
<div class="line">    <span class="keywordtype">int</span> <a class="code hl_variable" href="structagile__modbus__slave__util__map.html#a9b130b4a875dca0d63c31d81fbc8b0f1">start_addr</a>;                                       </div>
<div class="line">    <span class="keywordtype">int</span> <a class="code hl_variable" href="structagile__modbus__slave__util__map.html#ace624a13ffd6380963e9173d6f8f4eb9">end_addr</a>;                                         </div>
<div class="line">    int (*<a class="code hl_variable" href="structagile__modbus__slave__util__map.html#a7fd43329330824f98bdd5da3e8b8f170">get</a>)(<span class="keywordtype">void</span> *buf, <span class="keywordtype">int</span> bufsz);                     </div>
<div class="line">    int (*<a class="code hl_variable" href="structagile__modbus__slave__util__map.html#aeb703aff86edfb371edb56e0122d51ab">set</a>)(<span class="keywordtype">int</span> index, <span class="keywordtype">int</span> len, <span class="keywordtype">void</span> *buf, <span class="keywordtype">int</span> bufsz); </div>
<div class="line">} <a class="code hl_typedef" href="group___s_l_a_v_e___u_t_i_l___exported___types.html#ga704dafcb7fb13e228bfbf8744dd5a3f8">agile_modbus_slave_util_map_t</a>;</div>
<div class="ttc" id="agroup___s_l_a_v_e___u_t_i_l___exported___types_html_ga704dafcb7fb13e228bfbf8744dd5a3f8"><div class="ttname"><a href="group___s_l_a_v_e___u_t_i_l___exported___types.html#ga704dafcb7fb13e228bfbf8744dd5a3f8">agile_modbus_slave_util_map_t</a></div><div class="ttdeci">struct agile_modbus_slave_util_map agile_modbus_slave_util_map_t</div><div class="ttdoc">slave register mapping structure</div></div>
<div class="ttc" id="astructagile__modbus__slave__util__map_html_a7fd43329330824f98bdd5da3e8b8f170"><div class="ttname"><a href="structagile__modbus__slave__util__map.html#a7fd43329330824f98bdd5da3e8b8f170">agile_modbus_slave_util_map::get</a></div><div class="ttdeci">int(* get)(void *buf, int bufsz)</div><div class="ttdoc">Get register data interface.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00041">agile_modbus_slave_util.h:41</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util__map_html_a9b130b4a875dca0d63c31d81fbc8b0f1"><div class="ttname"><a href="structagile__modbus__slave__util__map.html#a9b130b4a875dca0d63c31d81fbc8b0f1">agile_modbus_slave_util_map::start_addr</a></div><div class="ttdeci">int start_addr</div><div class="ttdoc">Start address.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00039">agile_modbus_slave_util.h:39</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util__map_html_ace624a13ffd6380963e9173d6f8f4eb9"><div class="ttname"><a href="structagile__modbus__slave__util__map.html#ace624a13ffd6380963e9173d6f8f4eb9">agile_modbus_slave_util_map::end_addr</a></div><div class="ttdeci">int end_addr</div><div class="ttdoc">end address</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00040">agile_modbus_slave_util.h:40</a></div></div>
<div class="ttc" id="astructagile__modbus__slave__util__map_html_aeb703aff86edfb371edb56e0122d51ab"><div class="ttname"><a href="structagile__modbus__slave__util__map.html#aeb703aff86edfb371edb56e0122d51ab">agile_modbus_slave_util_map::set</a></div><div class="ttdeci">int(* set)(int index, int len, void *buf, int bufsz)</div><div class="ttdoc">Set register data interface.</div><div class="ttdef"><b>Definition:</b> <a href="agile__modbus__slave__util_8h_source.html#l00042">agile_modbus_slave_util.h:42</a></div></div>
</div><!-- fragment --><ul>
<li><b>Precautions</b>:<ul>
<li>The number of registers determined by the start address and end address is limited. Changing the size of the <code>map_buf</code> array inside the function can make it larger.<ul>
<li>bit register &lt; 250</li>
<li>register register &lt; 125</li>
</ul>
</li>
<li>The interface function is NULL, and the function code corresponding to the register can respond and is successful.</li>
</ul>
</li>
<li><p class="startli"><code>get</code> interface</p>
<p class="startli">Copy all data in the address field to <code>buf</code>.</p>
</li>
<li><p class="startli"><code>set</code> interface</p><ul>
<li><code>index</code>: offset within the address field</li>
<li><code>len</code>: length</li>
</ul>
<p class="startli">Modify data based on <code>index</code> and <code>len</code>.</p>
</li>
</ul>
</li>
</ul>
<h2><a class="anchor" id="autotoc_md30"></a>
2.4. Example</h2>
<ul>
<li>Examples on PC are provided in the <a href="./examples">examples</a> folder, which can be compiled and run under <code>WSL</code> or <code>Linux</code>.<ul>
<li>Examples of RTU/TCP master and slave</li>
<li><p class="startli">Examples of special function codes</p>
<p class="startli">RTU point-to-point transmission of files: Demonstrates the use of special function codes</p>
<p class="startli">RTU broadcast transmission file: Demonstrates the use of <code>frame_length</code> in <code>agile_modbus_slave_handle</code></p>
</li>
</ul>
</li>
<li><a href="https://github.com/loogg/agile_modbus_mcu_demos">mcu_demos</a> provides examples on MCU.</li>
<li><a href="https://github.com/loogg/AT32F437_Boot">AT32F437_Boot</a> A Bootloader based on RT-Thread implemented on AT32F437 that supports Modbus firmware upgrade.</li>
<li><a href="https://github.com/loogg/HPM6750_Boot">HPM6750_Boot</a> A Bootloader based on RT-Thread implemented on HPM6750 that supports Modbus firmware upgrade.</li>
</ul>
<h2><a class="anchor" id="autotoc_md31"></a>
2.5. Doxygen document generation</h2>
<ul>
<li>Use <code>Doxywizard</code> to open <a href="./doc/doxygen/Doxyfile">Doxyfile</a> and run it. The generated file will be under <a href="./doc/doxygen/output">doxygen/output</a>.</li>
<li><code>Graphviz</code> path needs to be changed.</li>
<li><code>HTML</code> is generated without using <code>chm</code> format. If it is enabled, you need to change the <code>hhc.exe</code> path.</li>
</ul>
<h1><a class="anchor" id="autotoc_md32"></a>
3. Support</h1>
<p ><img src="./figures/zanshang.jpg" alt="zanshang" class="inline"/></p>
<p >If Agile Modbus solves your problem, you might as well scan the QR code above and invite me for a <b>cup of coffee</b> ~</p>
<h1><a class="anchor" id="autotoc_md33"></a>
4. Contact information &amp; thanks</h1>
<ul>
<li>Maintenance: Ma Longwei</li>
<li>Home page: <a href="https://github.com/loogg/agile_modbus">https://github.com/loogg/agile_modbus</a></li>
<li>Email: <a href="#" onclick="location.href='mai'+'lto:'+'254'+'40'+'472'+'13'+'@qq'+'.c'+'om'; return false;">25440<span style="display: none;">.nosp@m.</span>4721<span style="display: none;">.nosp@m.</span>3@qq.<span style="display: none;">.nosp@m.</span>com</a> </li>
</ul>
</div></div><!-- PageDoc -->
</div><!-- contents -->
</div><!-- doc-content -->
<!-- start footer part -->
<div id="nav-path" class="navpath"><!-- id is needed for treeview function! -->
  <ul>
    <li class="footer">Generated by <a href="https://www.doxygen.org/index.html"><img class="footer" src="doxygen.svg" width="104" height="31" alt="doxygen"/></a> 1.9.2 </li>
  </ul>
</div>
</body>
</html>
